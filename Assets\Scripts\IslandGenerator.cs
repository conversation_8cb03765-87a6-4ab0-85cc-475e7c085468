using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

[RequireComponent(typeof(MeshFilter), typeof(MeshRenderer), typeof(MeshCollider))]
public class IslandGenerator : MonoBehaviour
{
    [Header("Grid Settings")]
    public int size = 50;
    public float surfaceHeight = 0f;
    public float edgeDepth = 5f;

    [Header("Noise Settings")]
    public float noiseScale = 10f;
    public float islandThreshold = 0.4f;
    public int seed = 0;

    [Header("Shape Settings")]
    [Range(16,256)] public int segments = 128;
    public float coreRadius = 25f;
    public float coastVariation = 5f;

    [Header("Appearance")]
    public Color grassColorLight = new Color(0.45f, 0.76f, 0.34f);
    public Color grassColorDark = new Color(0.41f, 0.69f, 0.31f);
    public Color cliffColor = new Color(0.36f, 0.25f, 0.2f);

    [Header("Checkerboard Settings")]
    [Range(0.5f, 5f)] public float tileSize = 1f;

    [Header("Generation Settings")]
    public bool generateOnStart = true;
    public bool autoUpdateInEditor = true;

    private MeshFilter meshFilter;
    private MeshCollider meshCollider;
    private MeshRenderer meshRenderer;

    void Start()
    {
        if (generateOnStart)
        {
            GenerateIsland();
        }
    }

    void OnValidate()
    {
        if (autoUpdateInEditor && Application.isPlaying)
        {
            GenerateIsland();
        }
    }

    public void GenerateIsland()
    {
        meshFilter = GetComponent<MeshFilter>();
        meshCollider = GetComponent<MeshCollider>();
        meshRenderer = GetComponent<MeshRenderer>();

        Mesh mesh = new Mesh();
        mesh.name = "ProceduralIsland";

        List<Vector3> vertices = new List<Vector3>();
        List<int> triangles = new List<int>();
        List<Color> colors = new List<Color>();
        List<Vector3> normals = new List<Vector3>();

        // --- noise setup ---
        System.Random rand = new System.Random(seed);
        Vector2 noiseOffset = new Vector2(rand.Next(0, 10000), rand.Next(0, 10000));

        // Step 1: Generate the jagged coastline ring
        List<Vector3> coastlinePoints = new List<Vector3>();
        float twoPi = Mathf.PI * 2f;

        for (int i = 0; i < segments; i++)
        {
            float angle = twoPi * i / segments;
            Vector2 dir = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle));
            float noise = Mathf.PerlinNoise(dir.x * noiseScale + noiseOffset.x, dir.y * noiseScale + noiseOffset.y);
            float radius = coreRadius + noise * coastVariation;

            Vector3 coastPoint = new Vector3(dir.x * radius, surfaceHeight, dir.y * radius);
            coastlinePoints.Add(coastPoint);
        }

        // Step 2: Find the bounding box of the island
        float minX = float.MaxValue, maxX = float.MinValue;
        float minZ = float.MaxValue, maxZ = float.MinValue;

        foreach (Vector3 point in coastlinePoints)
        {
            if (point.x < minX) minX = point.x;
            if (point.x > maxX) maxX = point.x;
            if (point.z < minZ) minZ = point.z;
            if (point.z > maxZ) maxZ = point.z;
        }

        // Step 3: Generate grid vertices inside the island polygon
        Dictionary<Vector2Int, int> gridVertexMap = new Dictionary<Vector2Int, int>();

        int startX = Mathf.FloorToInt(minX / tileSize);
        int endX = Mathf.CeilToInt(maxX / tileSize);
        int startZ = Mathf.FloorToInt(minZ / tileSize);
        int endZ = Mathf.CeilToInt(maxZ / tileSize);

        for (int x = startX; x <= endX; x++)
        {
            for (int z = startZ; z <= endZ; z++)
            {
                Vector3 gridPoint = new Vector3(x * tileSize, surfaceHeight, z * tileSize);

                // Check if this grid point is inside the island polygon
                if (IsPointInPolygon(new Vector2(gridPoint.x, gridPoint.z), coastlinePoints))
                {
                    // Add top vertex
                    int topIndex = vertices.Count;
                    vertices.Add(gridPoint);

                    // Checkerboard color based on grid coordinates
                    bool isLight = ((x + z) % 2) == 0;
                    colors.Add(isLight ? grassColorLight : grassColorDark);
                    normals.Add(Vector3.up);

                    // Add bottom vertex for cliff faces
                    vertices.Add(new Vector3(gridPoint.x, surfaceHeight - edgeDepth, gridPoint.z));
                    colors.Add(cliffColor);
                    normals.Add(Vector3.down);

                    gridVertexMap[new Vector2Int(x, z)] = topIndex;
                }
            }
        }

        // Step 4: Generate top surface quads
        for (int x = startX; x < endX; x++)
        {
            for (int z = startZ; z < endZ; z++)
            {
                Vector2Int current = new Vector2Int(x, z);
                Vector2Int right = new Vector2Int(x + 1, z);
                Vector2Int up = new Vector2Int(x, z + 1);
                Vector2Int upRight = new Vector2Int(x + 1, z + 1);

                // Check if all four corners of this quad are inside the island
                if (gridVertexMap.ContainsKey(current) && gridVertexMap.ContainsKey(right) &&
                    gridVertexMap.ContainsKey(up) && gridVertexMap.ContainsKey(upRight))
                {
                    int v00 = gridVertexMap[current];
                    int v10 = gridVertexMap[right];
                    int v01 = gridVertexMap[up];
                    int v11 = gridVertexMap[upRight];

                    // Add two triangles for the quad (top surface)
                    AddQuad(triangles, v00, v01, v11, v10);
                }
            }

        // Step 5: Generate cliff faces for edges
        GenerateCliffFaces(gridVertexMap, startX, endX, startZ, endZ, vertices, triangles, colors, normals);

        // Step 6: Add coastline ring vertices and cliff faces
        GenerateCoastlineCliffs(coastlinePoints, vertices, triangles, colors, normals);

        mesh.vertices = vertices.ToArray();
        mesh.triangles = triangles.ToArray();
        mesh.colors = colors.ToArray();
        mesh.normals = normals.ToArray();
        mesh.RecalculateBounds();

        meshFilter.sharedMesh = mesh;
        if (meshCollider != null) meshCollider.sharedMesh = mesh;

        // Create material
        Material mat = new Material(Shader.Find("Universal Render Pipeline/Particles/Unlit"));
        mat.SetColor("_BaseColor", Color.white);
        if (mat.HasProperty("_Cull")) mat.SetInt("_Cull", (int)UnityEngine.Rendering.CullMode.Off);
        if (mat.HasProperty("_CullMode")) mat.SetInt("_CullMode", (int)UnityEngine.Rendering.CullMode.Off);
        meshRenderer.sharedMaterial = mat;
    }

    bool IsPointInPolygon(Vector2 point, List<Vector3> polygon)
    {
        int count = 0;
        for (int i = 0; i < polygon.Count; i++)
        {
            Vector2 a = new Vector2(polygon[i].x, polygon[i].z);
            Vector2 b = new Vector2(polygon[(i + 1) % polygon.Count].x, polygon[(i + 1) % polygon.Count].z);

            if (((a.y > point.y) != (b.y > point.y)) &&
                (point.x < (b.x - a.x) * (point.y - a.y) / (b.y - a.y) + a.x))
            {
                count++;
            }
        }
        return (count % 2) == 1;
    }

    void GenerateCliffFaces(Dictionary<Vector2Int, int> gridVertexMap, int startX, int endX, int startZ, int endZ,
                           List<Vector3> vertices, List<int> triangles, List<Color> colors, List<Vector3> normals)
    {
        // Generate cliff faces for interior grid edges that border water/empty space
        for (int x = startX; x <= endX; x++)
        {
            for (int z = startZ; z <= endZ; z++)
            {
                Vector2Int current = new Vector2Int(x, z);
                if (!gridVertexMap.ContainsKey(current)) continue;

                int currentTop = gridVertexMap[current];
                int currentBottom = currentTop + 1;

                // Check each direction for cliff faces
                Vector2Int[] directions = {
                    new Vector2Int(1, 0),   // Right
                    new Vector2Int(-1, 0),  // Left
                    new Vector2Int(0, 1),   // Forward
                    new Vector2Int(0, -1)   // Back
                };

                Vector3[] cliffNormals = { Vector3.right, Vector3.left, Vector3.forward, Vector3.back };

                for (int d = 0; d < directions.Length; d++)
                {
                    Vector2Int neighbor = current + directions[d];

                    // If neighbor doesn't exist (is water), create cliff face
                    if (!gridVertexMap.ContainsKey(neighbor))
                    {
                        // Find the next vertex in the direction to form a cliff quad
                        Vector2Int nextInDirection = current;
                        if (d == 0 || d == 1) nextInDirection += new Vector2Int(0, 1); // Right/Left: move in Z
                        else nextInDirection += new Vector2Int(1, 0); // Forward/Back: move in X

                        if (gridVertexMap.ContainsKey(nextInDirection))
                        {
                            int nextTop = gridVertexMap[nextInDirection];
                            int nextBottom = nextTop + 1;

                            // Create cliff quad
                            if (d == 0) // Right face
                                AddQuad(triangles, currentBottom, nextBottom, nextTop, currentTop);
                            else if (d == 1) // Left face
                                AddQuad(triangles, nextBottom, currentBottom, currentTop, nextTop);
                            else if (d == 2) // Forward face
                                AddQuad(triangles, nextBottom, currentBottom, currentTop, nextTop);
                            else // Back face
                                AddQuad(triangles, currentBottom, nextBottom, nextTop, currentTop);
                        }
                    }
                }
            }
        }
    }

    void GenerateCoastlineCliffs(List<Vector3> coastlinePoints, List<Vector3> vertices, List<int> triangles,
                                List<Color> colors, List<Vector3> normals)
    {
        // Add coastline vertices and create cliff faces around the perimeter
        List<int> coastTopIndices = new List<int>();
        List<int> coastBottomIndices = new List<int>();

        // Add coastline vertices
        for (int i = 0; i < coastlinePoints.Count; i++)
        {
            Vector3 topPos = coastlinePoints[i];
            Vector3 bottomPos = new Vector3(topPos.x, surfaceHeight - edgeDepth, topPos.z);

            // Top vertex
            int topIndex = vertices.Count;
            vertices.Add(topPos);
            colors.Add(grassColorLight); // Coastline gets light color
            normals.Add(Vector3.up);
            coastTopIndices.Add(topIndex);

            // Bottom vertex
            int bottomIndex = vertices.Count;
            vertices.Add(bottomPos);
            colors.Add(cliffColor);
            normals.Add((topPos - Vector3.zero).normalized); // Outward normal
            coastBottomIndices.Add(bottomIndex);
        }

        // Create cliff quads around the coastline
        for (int i = 0; i < coastlinePoints.Count; i++)
        {
            int next = (i + 1) % coastlinePoints.Count;

            int topCurrent = coastTopIndices[i];
            int topNext = coastTopIndices[next];
            int bottomCurrent = coastBottomIndices[i];
            int bottomNext = coastBottomIndices[next];

            // Create cliff quad (facing outward)
            AddQuad(triangles, topCurrent, topNext, bottomNext, bottomCurrent);
        }
    }

    void AddQuad(List<int> triangles, int v1, int v2, int v3, int v4)
    {
        triangles.Add(v1);
        triangles.Add(v2);
        triangles.Add(v3);
        triangles.Add(v1);
        triangles.Add(v3);
        triangles.Add(v4);
    }

    public void ClearIsland()
    {
        if (meshFilter == null) meshFilter = GetComponent<MeshFilter>();
        if (meshCollider == null) meshCollider = GetComponent<MeshCollider>();

        if (meshFilter.sharedMesh != null)
        {
            DestroyImmediate(meshFilter.sharedMesh);
        }
        meshFilter.mesh = null;
        if (meshCollider.sharedMesh != null)
        {
            DestroyImmediate(meshCollider.sharedMesh);
        }
        meshCollider.sharedMesh = null;
    }
}

[CustomEditor(typeof(IslandGenerator))]
public class IslandGeneratorEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        IslandGenerator generator = (IslandGenerator)target;

        if (GUILayout.Button("Generate Island"))
        {
            generator.GenerateIsland();
        }

        if (GUILayout.Button("Clear Island"))
        {
            generator.ClearIsland();
        }
    }
}