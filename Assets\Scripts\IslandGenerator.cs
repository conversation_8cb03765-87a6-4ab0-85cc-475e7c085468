using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

[RequireComponent(typeof(MeshFilter), typeof(MeshRenderer), typeof(MeshCollider))]
public class IslandGenerator : MonoBehaviour
{
    [Header("Grid Settings")]
    public int size = 50;
    public float surfaceHeight = 0f;
    public float edgeDepth = 5f;

    [Header("Noise Settings")]
    public float noiseScale = 10f;
    public float islandThreshold = 0.4f;
    public int seed = 0;

    [Header("Shape Settings")]
    [Range(16,256)] public int segments = 128;
    public float coreRadius = 25f;
    public float coastVariation = 5f;

    [Header("Appearance")]
    public Color grassColorLight = new Color(0.45f, 0.76f, 0.34f);
    public Color grassColorDark = new Color(0.41f, 0.69f, 0.31f);
    public Color cliffColor = new Color(0.36f, 0.25f, 0.2f);

    [Header("Generation Settings")]
    public bool generateOnStart = true;
    public bool autoUpdateInEditor = true;

    private MeshFilter meshFilter;
    private MeshCollider meshCollider;
    private MeshRenderer meshRenderer;

    void Start()
    {
        if (generateOnStart)
        {
            GenerateIsland();
        }
    }

    void OnValidate()
    {
        if (autoUpdateInEditor && Application.isPlaying)
        {
            GenerateIsland();
        }
    }

    public void GenerateIsland()
    {
        meshFilter = GetComponent<MeshFilter>();
        meshCollider = GetComponent<MeshCollider>();
        meshRenderer = GetComponent<MeshRenderer>();

        Mesh mesh = new Mesh();
        mesh.name = "ProceduralIsland";

        List<Vector3> vertices = new List<Vector3>();
        List<int> triangles = new List<int>();
        List<Color> colors = new List<Color>();
        List<Vector3> normals = new List<Vector3>();

        // --- noise setup ---
        System.Random rand = new System.Random(seed);
        Vector2 noiseOffset = new Vector2(rand.Next(0, 10000), rand.Next(0, 10000));

        // Center top vertex (not strictly needed for checkerboard but makes a clean fan)
        int centerTopIndex = vertices.Count;
        vertices.Add(new Vector3(0, surfaceHeight, 0));
        colors.Add(((0 + 0) & 1) == 0 ? grassColorLight : grassColorDark); // arbitrary
        normals.Add(Vector3.up);

        int centerBottomIndex = vertices.Count;
        vertices.Add(new Vector3(0, surfaceHeight - edgeDepth, 0));
        colors.Add(cliffColor);
        normals.Add(Vector3.up); // point up, bottom is never visible

        // Precompute ring vertices
        int[] topRingIndices = new int[segments];
        int[] bottomRingIndices = new int[segments];

        float twoPi = Mathf.PI * 2f;
        for (int i = 0; i < segments; i++)
        {
            float angle = twoPi * i / segments;
            Vector2 dir = new Vector2(Mathf.Cos(angle), Mathf.Sin(angle));
            float noise = Mathf.PerlinNoise(dir.x * noiseScale + noiseOffset.x, dir.y * noiseScale + noiseOffset.y);
            float radius = coreRadius + noise * coastVariation;

            Vector3 topPos = new Vector3(dir.x * radius, surfaceHeight, dir.y * radius);
            Vector3 botPos = new Vector3(dir.x * radius, surfaceHeight - edgeDepth, dir.y * radius);

            // Determine colour (checkerboard alternating per segment)
            // Determine checkerboard colour based on world-space integer grid
            int ringIndex = Mathf.FloorToInt(radius);
            bool light = (((ringIndex + i) & 1) == 0);
            Color c = light ? grassColorLight : grassColorDark;

            // top vertex
            int topIdx = vertices.Count;
            vertices.Add(topPos);
            colors.Add(c);
            normals.Add(Vector3.up);
            topRingIndices[i] = topIdx;

            // bottom vertex
            int botIdx = vertices.Count;
            vertices.Add(botPos);
            colors.Add(cliffColor);
            normals.Add(dir.normalized); // outward
            bottomRingIndices[i] = botIdx;
        }

        // --- build top fan and cliff quads ---
        for (int i = 0; i < segments; i++)
        {
            int next = (i + 1) % segments;

            // Top triangle (fan)
            triangles.Add(centerTopIndex);
            triangles.Add(topRingIndices[next]);
            triangles.Add(topRingIndices[i]);

            // Cliff quad between segment edges
            int v00 = topRingIndices[i];
            int v01 = topRingIndices[next];
            int v11 = bottomRingIndices[next];
            int v10 = bottomRingIndices[i];

            // First triangle
            triangles.Add(v00);
            triangles.Add(v01);
            triangles.Add(v11);
            // Second triangle
            triangles.Add(v00);
            triangles.Add(v11);
            triangles.Add(v10);

            // Bottom cliff normals already set; ensure bottom vertices normals point outward too
            normals[v10] = (vertices[v10] - Vector3.zero).normalized;
            normals[v11] = (vertices[v11] - Vector3.zero).normalized;
        }

        mesh.vertices = vertices.ToArray();
        mesh.triangles = triangles.ToArray();
        mesh.colors = colors.ToArray();
        mesh.normals = normals.ToArray();
        mesh.RecalculateBounds();

        meshFilter.sharedMesh = mesh;
        if (meshCollider != null) meshCollider.sharedMesh = mesh;

        // material if not already
        Material mat = new Material(Shader.Find("Universal Render Pipeline/Particles/Unlit"));
        mat.SetColor("_BaseColor", Color.white);
        if (mat.HasProperty("_Cull")) mat.SetInt("_Cull", (int)UnityEngine.Rendering.CullMode.Off);
        if (mat.HasProperty("_CullMode")) mat.SetInt("_CullMode", (int)UnityEngine.Rendering.CullMode.Off);
        meshRenderer.sharedMaterial = mat;
    }

        /* BEGIN DUPLICATE GRID METHOD (commented out)
    }
        meshFilter = GetComponent<MeshFilter>();
        meshCollider = GetComponent<MeshCollider>();
        meshRenderer = GetComponent<MeshRenderer>();

        Mesh mesh = new Mesh();
        mesh.name = "ProceduralIsland";

        List<Vector3> vertices = new List<Vector3>();
        List<int> triangles = new List<int>();
        List<Color> colors = new List<Color>();
        List<Vector3> normals = new List<Vector3>();

        bool[,] isLand = new bool[size + 2, size + 2];
        System.Random rand = new System.Random(seed);
        Vector2 noiseOffset = new Vector2(rand.Next(0, 10000), rand.Next(0, 10000));

        float solidRadius = 0.25f;
        float coastWidth = 0.25f;

        for (int x = 0; x < size + 2; x++)
        {
            for (int z = 0; z < size + 2; z++)
            {
                float sampleX = (float)(x - 1) / size;
                float sampleZ = (float)(z - 1) / size;
                float distFromCenter = Vector2.Distance(new Vector2(sampleX, sampleZ), new Vector2(0.5f, 0.5f));

                if (distFromCenter < solidRadius)
                {
                    isLand[x, z] = true;
                }
                else if (distFromCenter < solidRadius + coastWidth)
                {
                    float noiseValue = Mathf.PerlinNoise(sampleX * noiseScale + noiseOffset.x, sampleZ * noiseScale + noiseOffset.y);
                    float coastT = (distFromCenter - solidRadius) / coastWidth;
                    if (noiseValue > coastT)
                    {
                        isLand[x, z] = true;
                    }
                }
            }
        }

        int[,] vertexMap = new int[size + 1, size + 1];

        for (int x = 0; x <= size; x++)
        {
            for (int z = 0; z <= size; z++)
            {
                vertexMap[x, z] = -1;
                if (isLand[x + 1, z + 1])
                {
                    float xPos = x - size / 2f;
                    float zPos = z - size / 2f;

                    // Top vertex
                    vertices.Add(new Vector3(xPos, surfaceHeight, zPos));
                    colors.Add((x + z) % 2 == 0 ? grassColorLight : grassColorDark);
                    normals.Add(Vector3.up);

                    // Bottom vertex
                    vertices.Add(new Vector3(xPos, surfaceHeight - edgeDepth, zPos));
                    colors.Add(cliffColor);
                    normals.Add(Vector3.zero); // Placeholder, will be calculated later

                    vertexMap[x, z] = vertices.Count - 2;
                }
            }
        }

        for (int x = 0; x <= size; x++)
        {
            for (int z = 0; z <= size; z++)
            {
                // Skip if this point is water
                if (vertexMap[x, z] == -1) continue;

                // --- Top Face Generation ---
                // Check if the neighbors to form a quad (right, top, top-right) are land
                if (x < size && z < size && vertexMap[x + 1, z] != -1 && vertexMap[x, z + 1] != -1 && vertexMap[x + 1, z + 1] != -1)
                {
                    int v00 = vertexMap[x, z];
                    int v10 = vertexMap[x + 1, z];
                    int v01 = vertexMap[x, z + 1];
                    int v11 = vertexMap[x + 1, z + 1];
                    AddQuad(triangles, v00, v01, v11, v10);
                }

                // --- Cliff Face Generation ---
                // Check Right (+X)
                if ((x == size || vertexMap[x + 1, z] == -1) && z < size)
                {
                    int v1 = vertexMap[x, z];
                    int v2 = vertexMap[x, z + 1];
                    if (v1 != -1 && v2 != -1)
                    {
                        AddQuad(triangles, v1 + 1, v2 + 1, v2, v1);
                        normals[v1 + 1] += Vector3.right; normals[v2 + 1] += Vector3.right;
                    }
                }

                // Check Left (-X)
                if ((x == 0 || vertexMap[x - 1, z] == -1) && z < size)
                {
                    int v1 = vertexMap[x, z];
                    int v2 = vertexMap[x, z + 1];
                    if (v1 != -1 && v2 != -1)
                    {
                        AddQuad(triangles, v2 + 1, v1 + 1, v1, v2);
                        normals[v1 + 1] += Vector3.left; normals[v2 + 1] += Vector3.left;
                    }
                }

                // Check Forward (+Z)
                if ((z == size || vertexMap[x, z + 1] == -1) && x < size)
                {
                    int v1 = vertexMap[x, z];
                    int v2 = vertexMap[x + 1, z];
                    if (v1 != -1 && v2 != -1)
                    {
                        AddQuad(triangles, v2 + 1, v1 + 1, v1, v2);
                        normals[v1 + 1] += Vector3.forward; normals[v2 + 1] += Vector3.forward;
                    }
                }

                // Check Back (-Z)
                if ((z == 0 || vertexMap[x, z - 1] == -1) && x < size)
                {
                    int v1 = vertexMap[x, z];
                    int v2 = vertexMap[x + 1, z];
                    if (v1 != -1 && v2 != -1)
                    {
                        AddQuad(triangles, v1 + 1, v2 + 1, v2, v1);
                        normals[v1 + 1] += Vector3.back; normals[v2 + 1] += Vector3.back;
                    }
                }
            }
        }

        for (int i = 0; i < normals.Count; i++)
        {
            if (normals[i] != Vector3.up)
            {
                normals[i] = normals[i].normalized;
            }
        }

        mesh.vertices = vertices.ToArray();
        mesh.triangles = triangles.ToArray();
        mesh.colors = colors.ToArray();
        mesh.normals = normals.ToArray();
        mesh.RecalculateBounds();

        meshFilter.mesh = mesh;
        if (meshCollider != null) meshCollider.sharedMesh = mesh;

        // Create a material that always shows vertex colours
        Material material = new Material(Shader.Find("Universal Render Pipeline/Particles/Unlit"));
        material.SetColor("_BaseColor", Color.white);
        // Disable back-face culling so cliffs are visible from every side
        if (material.HasProperty("_Cull"))
            material.SetInt("_Cull", (int)UnityEngine.Rendering.CullMode.Off);
        if (material.HasProperty("_CullMode"))
            material.SetInt("_CullMode", (int)UnityEngine.Rendering.CullMode.Off);
        END DUPLICATE GRID METHOD */

    void AddQuad(List<int> triangles, int v1, int v2, int v3, int v4)
    {
        triangles.Add(v1);
        triangles.Add(v2);
        triangles.Add(v3);
        triangles.Add(v1);
        triangles.Add(v3);
        triangles.Add(v4);
    }

    public void ClearIsland()
    {
        if (meshFilter == null) meshFilter = GetComponent<MeshFilter>();
        if (meshCollider == null) meshCollider = GetComponent<MeshCollider>();

        if (meshFilter.sharedMesh != null)
        {
            DestroyImmediate(meshFilter.sharedMesh);
        }
        meshFilter.mesh = null;
        if (meshCollider.sharedMesh != null)
        {
            DestroyImmediate(meshCollider.sharedMesh);
        }
        meshCollider.sharedMesh = null;
    }
}

[CustomEditor(typeof(IslandGenerator))]
public class IslandGeneratorEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        IslandGenerator generator = (IslandGenerator)target;

        if (GUILayout.Button("Generate Island"))
        {
            generator.GenerateIsland();
        }

        if (GUILayout.Button("Clear Island"))
        {
            generator.ClearIsland();
        }
    }
}