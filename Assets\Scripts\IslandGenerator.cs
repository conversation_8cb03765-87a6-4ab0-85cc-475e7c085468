using UnityEngine;
using System.Collections.Generic;

#if UNITY_EDITOR
using UnityEditor;
#endif

// This attribute ensures the necessary components are on the GameObject
[RequireComponent(typeof(MeshFilter), typeof(MeshRenderer))]
public class IslandGenerator : MonoBehaviour
{
    [Header("Island Shape")]
    [Range(5, 100)]
    public int islandRadius = 20;
    [Range(0.1f, 20f)]
    public float islandHeight = 5f;

    [Header("Terrain Noise")]
    [Range(0.01f, 1f)]
    public float noiseScale = 0.1f;
    public Vector2 noiseOffset = new Vector2(100, 100);

    [Header("Appearance")]
    public BiomeType biomeType = BiomeType.Grass;
    public float waterHeight = 0.5f;
    [Range(0.1f, 2f)]
    public float tileSize = 1f;
    [Range(0f, 0.5f)]
    public float colorVariation = 0.1f; // Controls tile-to-tile color variance

    [Header("Generation")]
    public bool generateOnStart = true;
    public bool autoUpdate = false;

    // Enum for Biome definitions
    public enum BiomeType { Grass, Dirt, Sand, Snow }

    // Mesh data lists
    private MeshFilter meshFilter;
    private MeshRenderer meshRenderer;
    private MeshCollider meshCollider;
    private List<Vector3> vertices = new List<Vector3>();
    private List<int> triangles = new List<int>();
    private List<Color> colors = new List<Color>();
    private Dictionary<Vector2Int, float> heightMap = new Dictionary<Vector2Int, float>();

    void Start()
    {
        if (generateOnStart)
        {
            GenerateIsland();
        }
    }

    private void OnValidate()
    {
        // This allows the island to update in real-time in the editor
        if (autoUpdate && Application.isEditor)
        {
            GenerateIsland();
        }
    }

    public void GenerateIsland()
    {
        // FIX: Proactively get or add components to prevent errors
        meshFilter = gameObject.GetComponent<MeshFilter>();
        if (meshFilter == null) meshFilter = gameObject.AddComponent<MeshFilter>();

        meshRenderer = gameObject.GetComponent<MeshRenderer>();
        if (meshRenderer == null) meshRenderer = gameObject.AddComponent<MeshRenderer>();

        meshCollider = gameObject.GetComponent<MeshCollider>();
        if (meshCollider == null) meshCollider = gameObject.AddComponent<MeshCollider>();

        // Generation process
        ClearMeshData();
        CreateHeightMap();
        CreateMesh();
        ApplyMesh();
    }

    void ClearMeshData()
    {
        vertices.Clear();
        triangles.Clear();
        colors.Clear();
        heightMap.Clear();
    }

    void CreateHeightMap()
    {
        // Use a random offset for varied islands if you want
        Vector2 trueOffset = new Vector2(noiseOffset.x, noiseOffset.y);

        for (int q = -islandRadius; q <= islandRadius; q++)
        {
            int r1 = Mathf.Max(-islandRadius, -q - islandRadius);
            int r2 = Mathf.Min(islandRadius, -q + islandRadius);
            for (int r = r1; r <= r2; r++)
            {
                Vector2Int hexCoords = new Vector2Int(q, r);
                Vector3 worldPos = HexToWorld(hexCoords);

                // Create a circular falloff map to form the island shape
                float distance = Mathf.Sqrt(worldPos.x * worldPos.x + worldPos.z * worldPos.z);
                float falloffRadius = islandRadius * tileSize * 0.9f;
                float falloff = Mathf.Pow(Mathf.Clamp01(1.0f - distance / falloffRadius), 2);

                // Generate Perlin noise for terrain height
                float noise = Mathf.PerlinNoise(
                    (worldPos.x + trueOffset.x) * noiseScale,
                    (worldPos.z + trueOffset.y) * noiseScale
                );
                
                float height = noise * islandHeight * falloff;

                if (height > waterHeight)
                {
                    heightMap[hexCoords] = height;
                }
            }
        }
    }

    void CreateMesh()
    {
        foreach (Vector2Int hex in heightMap.Keys)
        {
            CreateHexTile(hex);
        }
    }
    
    void CreateHexTile(Vector2Int hex)
    {
        float height = heightMap[hex];
        Vector3 centerPos = HexToWorld(hex, height);
        
        Vector3[] topVerts = new Vector3[6];
        for (int i = 0; i < 6; i++)
        {
            topVerts[i] = centerPos + HexCorner(i);
        }

        // Get the single color for this entire hex tile, with variation applied
        Color tileColor = GetColorForHeight(height, centerPos);

        // Create the 6 triangles for the top face
        for (int i = 0; i < 6; i++)
        {
            // Swapped the last two vertices to make the triangle face upwards
            AddTriangle(
                centerPos,
                topVerts[(i + 1) % 6],
                topVerts[i],
                tileColor
            );
        }

        // Create side walls for edges that border water
        for (int i = 0; i < 6; i++)
        {
            Vector2Int neighbor = hex + hex_directions[i];
            if (!heightMap.ContainsKey(neighbor)) // If neighbor is water
            {
                Vector3 v1 = topVerts[i];
                Vector3 v2 = topVerts[(i + 1) % 6];
                
                AddQuad(
                    v1, 
                    v2, 
                    new Vector3(v1.x, 0, v1.z), 
                    new Vector3(v2.x, 0, v2.z),
                    GetSideColor()
                );
            }
        }
    }

    void ApplyMesh()
    {
        Mesh mesh = new Mesh();
        mesh.vertices = vertices.ToArray();
        mesh.triangles = triangles.ToArray();
        mesh.colors = colors.ToArray();

        mesh.RecalculateNormals();
        mesh.RecalculateBounds();
        
        meshFilter.mesh = mesh;
        meshCollider.sharedMesh = mesh;
        
        if (meshRenderer.sharedMaterial == null)
        {
            Material material = new Material(Shader.Find("Universal Render Pipeline/Lit"));
            meshRenderer.sharedMaterial = material;
        }
    }

    #region Mesh Helpers
    
    void AddTriangle(Vector3 v1, Vector3 v2, Vector3 v3, Color color)
    {
        int vertexIndex = vertices.Count;
        vertices.Add(v1);
        vertices.Add(v2);
        vertices.Add(v3);
        triangles.Add(vertexIndex);
        triangles.Add(vertexIndex + 1);
        triangles.Add(vertexIndex + 2);
        colors.Add(color);
        colors.Add(color);
        colors.Add(color);
    }

void AddQuad(Vector3 v1, Vector3 v2, Vector3 v3, Vector3 v4, Color color)
    {
        int vertexIndex = vertices.Count;
        vertices.Add(v1);
        vertices.Add(v2);
        vertices.Add(v3);
        vertices.Add(v4);
        
        // Correct, Counter-Clockwise Winding Order
        // This makes the quad face outwards instead of inwards.
        triangles.Add(vertexIndex);      // Top-Left
        triangles.Add(vertexIndex + 1);  // Top-Right
        triangles.Add(vertexIndex + 2);  // Bottom-Left

        triangles.Add(vertexIndex + 1);  // Top-Right
        triangles.Add(vertexIndex + 3);  // Bottom-Right
        triangles.Add(vertexIndex + 2);  // Bottom-Left
        
        colors.Add(color);
        colors.Add(color);
        colors.Add(color);
        colors.Add(color);
    }
    
    #endregion

    #region Hexagonal Grid Math
    
    private static readonly Vector2Int[] hex_directions = new Vector2Int[] {
        new Vector2Int(1, 0), new Vector2Int(1, -1), new Vector2Int(0, -1),
        new Vector2Int(-1, 0), new Vector2Int(-1, 1), new Vector2Int(0, 1)
    };

    Vector3 HexToWorld(Vector2Int hex, float height = 0)
    {
        float x = tileSize * 1.5f * hex.x;
        float z = tileSize * Mathf.Sqrt(3) * (hex.y + hex.x / 2.0f);
        return new Vector3(x, height, z);
    }

    Vector3 HexCorner(int i)
    {
        float angle_deg = 60 * i + 30; // Pointy-topped hex corner
        float angle_rad = Mathf.PI / 180f * angle_deg;
        return new Vector3(tileSize * Mathf.Cos(angle_rad), 0, tileSize * Mathf.Sin(angle_rad));
    }
    
    #endregion
    
    #region Biome Coloring
    
    Color GetColorForHeight(float height, Vector3 position)
    {
        // Step 1: Determine the base color from the biome and height
        Color baseColor;
        switch (biomeType)
        {
            case BiomeType.Grass:
                if (height > islandHeight * 0.7f) baseColor = new Color(0.6f, 0.6f, 0.6f); // Rock
                else baseColor = new Color(0.4f, 0.8f, 0.2f); // Grass
                break;
            case BiomeType.Sand:
                baseColor = new Color(0.9f, 0.8f, 0.6f);
                break;
            case BiomeType.Dirt:
                baseColor = new Color(0.6f, 0.4f, 0.2f);
                break;
            case BiomeType.Snow:
                if (height > islandHeight * 0.5f) baseColor = new Color(0.95f, 0.95f, 1f); // Snow
                else baseColor = new Color(0.5f, 0.5f, 0.5f); // Rock
                break;
            default:
                baseColor = Color.magenta; // Error color
                break;
        }

        // Step 2: Apply slight random variation based on world position
        if (colorVariation > 0)
        {
            float noise = (Mathf.PerlinNoise(position.x * 0.8f, position.z * 0.8f) - 0.5f) * 2f; // Noise from -1 to 1
            float l, s, v;
            Color.RGBToHSV(baseColor, out l, out s, out v);
            v += noise * colorVariation; // Adjust the brightness (Value)
            baseColor = Color.HSVToRGB(l, s, v);
        }

        return baseColor;
    }
    
    Color GetSideColor()
    {
         switch (biomeType)
        {
            case BiomeType.Grass: return new Color(0.44f, 0.3f, 0.15f); // Dirt
            case BiomeType.Sand: return new Color(0.7f, 0.6f, 0.4f);
            case BiomeType.Dirt: return new Color(0.4f, 0.25f, 0.1f);
            case BiomeType.Snow: return new Color(0.4f, 0.4f, 0.4f); // Dark Rock
            default: return Color.grey;
        }
    }
    
    #endregion

    public void ClearIsland()
    {
        if (meshFilter) meshFilter.sharedMesh = null;
        if (meshCollider) meshCollider.sharedMesh = null;
    }
}

#if UNITY_EDITOR
[CustomEditor(typeof(IslandGenerator))]
public class IslandGeneratorEditor : Editor
{
    public override void OnInspectorGUI()
    {
        IslandGenerator generator = (IslandGenerator)target;
        
        // Draw the default inspector and check if a value was changed
        if (DrawDefaultInspector())
        {
            if (generator.autoUpdate)
            {
                generator.GenerateIsland();
            }
        }

        if (GUILayout.Button("Generate Island"))
        {
            generator.GenerateIsland();
        }

        if (GUILayout.Button("Clear Island"))
        {
            generator.ClearIsland();
        }
    }
}
#endif