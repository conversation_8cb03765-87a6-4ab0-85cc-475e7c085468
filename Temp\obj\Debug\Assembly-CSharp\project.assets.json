{"version": 3, "targets": {".NETStandard,Version=v2.1": {"Assembly-CSharp-firstpass/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "dependencies": {"Sirenix.OdinInspector.Modules.UnityMathematics": "1.0.0"}, "compile": {"bin/placeholder/Assembly-CSharp-firstpass.dll": {}}, "runtime": {"bin/placeholder/Assembly-CSharp-firstpass.dll": {}}}, "Sirenix.OdinInspector.Modules.UnityMathematics/1.0.0": {"type": "project", "framework": ".NETStandard,Version=v2.1", "compile": {"bin/placeholder/Sirenix.OdinInspector.Modules.UnityMathematics.dll": {}}, "runtime": {"bin/placeholder/Sirenix.OdinInspector.Modules.UnityMathematics.dll": {}}}}}, "libraries": {"Assembly-CSharp-firstpass/1.0.0": {"type": "project", "path": "Assembly-CSharp-firstpass.csproj", "msbuildProject": "Assembly-CSharp-firstpass.csproj"}, "Sirenix.OdinInspector.Modules.UnityMathematics/1.0.0": {"type": "project", "path": "Sirenix.OdinInspector.Modules.UnityMathematics.csproj", "msbuildProject": "Sirenix.OdinInspector.Modules.UnityMathematics.csproj"}}, "projectFileDependencyGroups": {".NETStandard,Version=v2.1": ["Assembly-CSharp-firstpass >= 1.0.0", "Sirenix.OdinInspector.Modules.UnityMathematics >= 1.0.0"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}, "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\- Unity Projects\\Idle Dungeon Town\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "D:\\- Unity Projects\\Idle Dungeon Town\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\- Unity Projects\\Idle Dungeon Town\\Temp\\obj\\Debug\\Assembly-CSharp\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\- Unity Projects\\Idle Dungeon Town\\Assembly-CSharp-firstpass.csproj": {"projectPath": "D:\\- Unity Projects\\Idle Dungeon Town\\Assembly-CSharp-firstpass.csproj"}, "D:\\- Unity Projects\\Idle Dungeon Town\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj": {"projectPath": "D:\\- Unity Projects\\Idle Dungeon Town\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}