Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.34f1 (5ab2d9ed9190) revision 5944025'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.26100) 64bit Professional' Language: 'en' Physical Memory: 32656 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
C:\Program Files\Unity\Hub\Editor\6000.0.34f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker1
-projectPath
D:/- Unity Projects/Idle Dungeon Town
-logFile
Logs/AssetImportWorker1.log
-srvPort
58681
-job-worker-count
7
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: D:/- Unity Projects/Idle Dungeon Town
D:/- Unity Projects/Idle Dungeon Town
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [49308]  Target information:

Player connection [49308]  * "[IP] ******** [Port] 0 [Flags] 2 [Guid] 2174882997 [EditorId] 2174882997 [Version] 1048832 [Id] WindowsEditor(7,DESKTOP-IOER97B) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [49308] Host joined multi-casting on [***********:54997]...
Player connection [49308] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 7
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 432.54 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.34f1 (5ab2d9ed9190)
[Subsystems] Discovering subsystems at path C:/Program Files/Unity/Hub/Editor/6000.0.34f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path D:/- Unity Projects/Idle Dungeon Town/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1060 6GB (ID=0x1c03)
    Vendor:   NVIDIA
    VRAM:     6029 MB
    Driver:   32.0.15.6614
Initialize mono
Mono path[0] = 'C:/Program Files/Unity/Hub/Editor/6000.0.34f1/Editor/Data/Managed'
Mono path[1] = 'C:/Program Files/Unity/Hub/Editor/6000.0.34f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'C:/Program Files/Unity/Hub/Editor/6000.0.34f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56688
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.34f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.34f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Register platform support module: C:/Program Files/Unity/Hub/Editor/6000.0.34f1/Editor/Data/PlaybackEngines/AndroidPlayer/UnityEditor.Android.Extensions.dll
Registered in 0.710113 seconds.
- Loaded All Assemblies, in 11.642 seconds
Native extension for Android target not found
Android Extension - Scanning For ADB Devices 362 ms
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.788 seconds
Domain Reload Profiling: 12428ms
	BeginReloadAssembly (9095ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (231ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (765ms)
	LoadAllAssembliesAndSetupDomain (1536ms)
		LoadAssemblies (9092ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1531ms)
			TypeCache.Refresh (1529ms)
				TypeCache.ScanAssembly (1508ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (788ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (752ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (476ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (53ms)
			ProcessInitializeOnLoadAttributes (140ms)
			ProcessInitializeOnLoadMethodAttributes (79ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
Symbol file data-000002BA7B2D56E0 doesn't match image D:\- Unity Projects\Idle Dungeon Town\Assets\Plugins\Sirenix\Assemblies\Sirenix.OdinInspector.Editor.dll
Symbol file LoadedFromMemory is not a mono symbol file
Symbol file LoadedFromMemory is not a mono symbol file
- Loaded All Assemblies, in 14.037 seconds
Refreshing native plugins compatible for Editor in 3.06 ms, found 2 plugins.
Native extension for Android target not found
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Launched and connected shader compiler UnityShaderCompiler.exe after 0.03 seconds
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  2.372 seconds
Domain Reload Profiling: 16407ms
	BeginReloadAssembly (186ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (40ms)
	RebuildCommonClasses (49ms)
	RebuildNativeTypeToScriptingClass (13ms)
	initialDomainReloadingComplete (41ms)
	LoadAllAssembliesAndSetupDomain (13746ms)
		LoadAssemblies (12642ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (1212ms)
			TypeCache.Refresh (1117ms)
				TypeCache.ScanAssembly (1030ms)
			BuildScriptInfoCaches (75ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (2372ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1852ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (13ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (104ms)
			ProcessInitializeOnLoadAttributes (682ms)
			ProcessInitializeOnLoadMethodAttributes (1045ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (8ms)
Refreshing native plugins compatible for Editor in 4.50 ms, found 2 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 250 Unused Serialized files (Serialized files now loaded: 0)
Unloading 5308 unused Assets / (4.9 MB). Loaded Objects now: 6058.
Memory consumption went from 197.4 MB to 192.5 MB.
Total: 38.711800 ms (FindLiveObjects: 1.873600 ms CreateObjectMapping: 1.797900 ms MarkObjects: 29.517500 ms  DeleteObjects: 5.521200 ms)

========================================================================
Received Import Request.
  Time since last request: 3295139.562933 seconds.
  path: Assets/Scripts/Inventory/Tooltip.cs
  artifactKey: Guid(f9de67cfdd594e54daa37d2ca22730d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Scripts/Inventory/Tooltip.cs using Guid(f9de67cfdd594e54daa37d2ca22730d1) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8377207bb6de6df66ef51b2b00e88bcf') in 0.0065924 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

