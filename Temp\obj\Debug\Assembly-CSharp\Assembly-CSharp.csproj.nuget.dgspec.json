{"format": 1, "restore": {"D:\\- Unity Projects\\Idle Dungeon Town\\Assembly-CSharp.csproj": {}}, "projects": {"D:\\- Unity Projects\\Idle Dungeon Town\\Assembly-CSharp-firstpass.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\- Unity Projects\\Idle Dungeon Town\\Assembly-CSharp-firstpass.csproj", "projectName": "Assembly-CSharp-firstpass", "projectPath": "D:\\- Unity Projects\\Idle Dungeon Town\\Assembly-CSharp-firstpass.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\- Unity Projects\\Idle Dungeon Town\\Temp\\obj\\Debug\\Assembly-CSharp-firstpass\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\- Unity Projects\\Idle Dungeon Town\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj": {"projectPath": "D:\\- Unity Projects\\Idle Dungeon Town\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "D:\\- Unity Projects\\Idle Dungeon Town\\Assembly-CSharp.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\- Unity Projects\\Idle Dungeon Town\\Assembly-CSharp.csproj", "projectName": "Assembly-CSharp", "projectPath": "D:\\- Unity Projects\\Idle Dungeon Town\\Assembly-CSharp.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\- Unity Projects\\Idle Dungeon Town\\Temp\\obj\\Debug\\Assembly-CSharp\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {"D:\\- Unity Projects\\Idle Dungeon Town\\Assembly-CSharp-firstpass.csproj": {"projectPath": "D:\\- Unity Projects\\Idle Dungeon Town\\Assembly-CSharp-firstpass.csproj"}, "D:\\- Unity Projects\\Idle Dungeon Town\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj": {"projectPath": "D:\\- Unity Projects\\Idle Dungeon Town\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}, "D:\\- Unity Projects\\Idle Dungeon Town\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\- Unity Projects\\Idle Dungeon Town\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj", "projectName": "Sirenix.OdinInspector.Modules.UnityMathematics", "projectPath": "D:\\- Unity Projects\\Idle Dungeon Town\\Sirenix.OdinInspector.Modules.UnityMathematics.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\- Unity Projects\\Idle Dungeon Town\\Temp\\obj\\Debug\\Sirenix.OdinInspector.Modules.UnityMathematics\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netstandard2.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netstandard2.1": {"targetAlias": "netstandard2.1", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.301\\RuntimeIdentifierGraph.json"}}}}}